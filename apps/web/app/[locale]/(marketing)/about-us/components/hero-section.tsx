'use client'
import { useState, useEffect } from 'react'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import Body from './typography/body'
import Heading from './typography/heading'
import { cn } from '@ui/lib'

interface MediaItem {
  id: number
  type: 'image' | 'video'
  src: string
  alt?: string
}

interface AboutCarouselProps {
  items: MediaItem[]
  autoPlay?: boolean
  autoPlayInterval?: number
  className?: string
}

function AboutCarousel({
  items,
  autoPlay = true,
  autoPlayInterval = 5000,
  className,
}: AboutCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isAnimating, setIsAnimating] = useState(false)
  const [isPaused, setIsPaused] = useState(false)

  // 自动播放
  useEffect(() => {
    if (!autoPlay || isPaused) return
    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) =>
        prevIndex === items.length - 1 ? 0 : prevIndex + 1
      )
    }, autoPlayInterval)

    return () => {
      clearInterval(interval)
    }
  }, [autoPlay, autoPlayInterval, items.length, isPaused])

  const handlePrevious = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex === 0 ? items.length - 1 : prevIndex - 1
    )
  }

  const handleNext = () => {
    setIsAnimating(true)
    setCurrentIndex((prevIndex) =>
      prevIndex === items.length - 1 ? 0 : prevIndex + 1
    )
    setTimeout(() => {
      setIsAnimating(false)
    }, 300)
  }

  const goToSlide = (index: number) => {
    setCurrentIndex(index)
  }

  if (!items.length) return null

  const currentItem = items[currentIndex]

  return (
    <div
      className={cn(
        'group relative aspect-[4/3] max-w-3xl mx-auto bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100',
        className
      )}
      onMouseEnter={() => setIsPaused(true)}
      onMouseLeave={() => setIsPaused(false)}
    >
      {/* 媒体内容 */}
      <div className="absolute inset-0">
        {currentItem.type === 'video' ? (
          <video
            key={currentItem.src}
            className={cn(
              'w-full h-full object-cover transition-all duration-1000 ease-out',
              isAnimating ? 'scale-105 opacity-95' : 'scale-100 opacity-100'
            )}
            autoPlay
            muted
            loop
            playsInline
          >
            <source src={currentItem.src} type="video/mp4" />
          </video>
        ) : (
          <img
            src={currentItem.src}
            alt={currentItem.alt || ''}
            className={cn(
              'w-full h-full object-cover transition-all duration-1000 ease-out',
              isAnimating ? 'scale-105 opacity-95' : 'scale-100 opacity-100'
            )}
          />
        )}

        {/* 极简渐变遮罩 */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/3 via-transparent to-transparent" />
      </div>

      {/* 导航箭头 - 更简洁的设计 */}
      <button
        onClick={handlePrevious}
        className="absolute left-3 top-1/2 -translate-y-1/2 z-20 p-3 transition-all duration-300 text-gray-600/60 hover:text-gray-800 hover:bg-white/80 rounded-full hover:scale-105 backdrop-blur-sm shadow-sm opacity-0 group-hover:opacity-100"
        aria-label="Previous slide"
      >
        <ChevronLeft className="w-4 h-4" />
      </button>

      <button
        onClick={handleNext}
        className="absolute right-3 top-1/2 -translate-y-1/2 z-20 p-3 transition-all duration-300 text-gray-600/60 hover:text-gray-800 hover:bg-white/80 rounded-full hover:scale-105 backdrop-blur-sm shadow-sm opacity-0 group-hover:opacity-100"
        aria-label="Next slide"
      >
        <ChevronRight className="w-4 h-4" />
      </button>

      {/* 指示器 - 更优雅的设计 */}
      <div className="absolute bottom-6 left-1/2 -translate-x-1/2 z-20 flex space-x-3">
        {items.map((_, index) => (
          <button
            key={index}
            onClick={() => goToSlide(index)}
            className={cn(
              'transition-all duration-500 rounded-full',
              index === currentIndex
                ? 'w-8 h-2 bg-gray-800 shadow-md'
                : 'w-2 h-2 bg-gray-400/60 hover:bg-gray-600/80 hover:scale-125'
            )}
            aria-label={`Go to slide ${index + 1}`}
          />
        ))}
      </div>
    </div>
  )
}

export default function HeroSection() {
  // 轮播图媒体项目
  const carouselItems: MediaItem[] = [
    {
      id: 1,
      type: 'image',
      src: '/images/about/wood.png',
      alt: 'Hand carved wooden animals showcasing traditional wood carving craftsmanship',
    },
    {
      id: 2,
      type: 'image',
      src: '/images/about/woods.webp',
      alt: 'Beautiful wooden sculptures displaying the artistry of wood carving',
    },
    {
      id: 3,
      type: 'image',
      src: '/images/about/wood.webp',
      alt: 'A stunning collection of hand carved wooden animals arranged artfully on natural oak',
    },
    {
      id: 4,
      type: 'video',
      src: '/images/about/wood.mp4',
      alt: 'Wood carving process demonstration video',
    },
  ]

  return (
    <section className="relative min-h-screen flex items-center justify-center bg-white overflow-hidden">
      {/* Minimalist background pattern */}
      <div className="absolute inset-0 opacity-10">
        <div
          className="absolute inset-0"
          style={{
            backgroundImage: `radial-gradient(circle at 25% 25%, rgb(148 163 184) 1px, transparent 1px),
                           radial-gradient(circle at 75% 75%, rgb(148 163 184) 1px, transparent 1px)`,
            backgroundSize: '80px 80px',
          }}
        ></div>
      </div>

      {/* Main content */}
      <div className="relative z-10 max-w-5xl mx-auto px-6 lg:px-8">
        <div className="text-center space-y-12">
          {/* Zen-inspired divider */}
          <div className="flex items-center justify-center space-x-4 mb-8">
            <div className="w-12 h-px bg-gray-300"></div>
            <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
            <div className="w-12 h-px bg-gray-300"></div>
          </div>

          <Heading
            className="text-gray-900 leading-tight tracking-tight max-w-4xl mx-auto"
            desktopSize="6xl"
            font="serif"
            mobileSize="3xl"
            tag="h1"
          >
            Cokugoo: Where Wood Carving Art Finds Its Soul
          </Heading>

          <div className="max-w-3xl mx-auto">
            <Body
              className="text-gray-700 leading-relaxed"
              desktopSize="xl"
              font="serif"
              mobileSize="lg"
            >
              In a world of fleeting digital noise, we find truth in the
              tangible grain of wood. At cokugoo, we don't just craft objects;
              we channel the spirit of the wild into timeless pieces through the
              cherished tradition of wood carving. Each of our creations is a
              bridge between the heart of the forest and the soul of your home.
            </Body>
          </div>

          {/* 轮播图区域 */}
          <div className="mt-16 relative">
            <AboutCarousel
              items={carouselItems}
              autoPlay={true}
              autoPlayInterval={4000}
            />
          </div>

          {/* Bottom zen divider */}
          <div className="flex items-center justify-center space-x-4 mt-16">
            <div className="w-8 h-px bg-gray-300"></div>
            <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
            <div className="w-16 h-px bg-gray-300"></div>
            <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
            <div className="w-8 h-px bg-gray-300"></div>
          </div>
        </div>
      </div>
    </section>
  )
}
